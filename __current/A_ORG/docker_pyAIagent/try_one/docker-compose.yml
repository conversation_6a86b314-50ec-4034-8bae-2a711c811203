version: '3.8'

services:
  fastapi:
    build: ./backend
    container_name: fastapi_service
    ports:
      - "8000:80"
    volumes:
      - ./backend:/app
    environment:
      - PYTHONUNBUFFERED=1
    depends_on:
      - gradio
    command: ["sh", "-c", "pip install -r requirements.txt && uvicorn backend.main:app --host 0.0.0.0 --port 80"]

  gradio:
    build: ./frontend
    container_name: gradio_service
    ports:
      - "7860:7860"
    volumes:
      - ./frontend:/app
    depends_on:
      - fastapi
    command: ["python", "app.py"]

