# https://medium.com/@mayvic/scalable-streaming-of-openai-model-responses-with-fastapi-and-asyncio-714744b13dd


# https://fastapi.tiangolo.com/tutorial/body/

from pydantic import BaseModel
from typing import Optional


from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from starlette.responses import StreamingResponse
from openai import AsyncOpenAI



from inference_request import InferenceRequest

OPENAI_API_KEY="***************************************************"
app = FastAPI()

import async_timeout
import asyncio

GENERATION_TIMEOUT_SEC = 60

async def stream_generator(subscription):
    async with async_timeout.timeout(GENERATION_TIMEOUT_SEC):
        try:
            async for chunk in subscription:
                yield post_processing(chunk)
        except asyncio.TimeoutError:
            raise HTTPException(status_code=504, detail="Stream timed out")


client = AycncOpenAI()

@app.post(f"/openai_streaming")
async def openai_streaming(request: InferenceRequest) -> StreamingResponse: 
  try:    
    #   subscription = await openai.ChatCompletion.acreate(
        subscription = await client.chat.completions.acreate(
        #   model = "gpt-4o",
        #   api_key = OPENAI_API_KEY,
          model=request.model_name,
          api_key=request.api_key,
          organization=request.org_id,
          messages=input_text,
          stream=True,
          **request.generation_cfg
      )
  

      return StreamingResponse(stream_generator(subscription),
                               media_type='text/event-stream')
  except openai.OpenAIError:
    raise HTTPException(status_code=500, detail='OpenAI call failed')



