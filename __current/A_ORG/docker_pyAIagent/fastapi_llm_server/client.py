import httpx
from pydantic import BaseModel
from typing import Optional
from inference_request import InferenceRequest

ENDPOINT_URL = 'http://0.0.0.0:8000/openai_streaming'

OPENAI_API_KEY='***************************************************'

payload = InferenceRequest(api_key='***************************************************', 
                           input_text='What is the capital of Italy?',
                           model_name='gpt-4o').dict()

with httpx.stream('POST', ENDPOINT_URL, json=payload) as r:
     for chunk in r.iter_raw():        
         print(chunk)