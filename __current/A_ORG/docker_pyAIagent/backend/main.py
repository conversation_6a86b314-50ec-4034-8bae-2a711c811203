from fastapi import FastAPI
import requests
from pydantic import BaseModel



class Body(BaseModel):
    text: str


app = FastAPI()


@app.get("/")
def read_root():
    return {"message": "Hello from FastAPI!"}

@app.post("/search")
async def generate_response(body: Body):
    user_prompt = body.text
    result = await search_agent.run(user_prompt)
    #result = generate(question, llm, PromptTemplate)
    return {"response": result.data}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=80)