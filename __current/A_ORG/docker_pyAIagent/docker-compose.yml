version: '3.8'

services:
  fastapi:
    build:
      context: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  streamlit:
    build:
      context: ./frontend
    ports:
      - "8501:8501"
    volumes:
      - ./frontend:/app
    command: streamlit run app.py --server.port=8501
    depends_on:
      - fastapi

