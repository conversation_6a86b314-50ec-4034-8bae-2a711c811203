import os
from dotenv import load_dotenv
import asyncio
from pydantic_ai import Agent, RunContext
from tavily import TavilyClient, AsyncTavilyClient
# import logfire



# logfire_configure()
# Load environment variables from .env file
load_dotenv()

# logfire.instrument-fastapi(app)
# Configure logfire
# logfire.configure(api_key=os.getenv('LOGFIRE_API'))

# Access other API keys
openai_api_key = os.getenv('OPENAI_API_KEY')
tavily_api_key = os.getenv('TAVILY_API_KEY')


# fastapi call does not bleonv ign front end
# @app.get('/llm/ask/stream/{chat_id}')
# async def llm_stream(chat_id: str) -> StreamingResponse



search_agent = Agent(  
    'openai:gpt-4',
    #deps_type=int,
    result_type=str,
    system_prompt=(
        'If the information related to the user question is not availabe, should use the talivy tool'
    ),
)


@search_agent.tool
async def talivy_tool(ctx: RunContext, query:str):  
    """useful to find the latest information from internet"""
    tavily_client = AsyncTavilyClient(api_key=os.environ["TAVILY_API_KEY"])
    response = await tavily_client.search(query, max_results=3)
    return response['results']


# Run the agent
async def run_agent(user_query):
    result = await search_agent.run(user_query)
    return result

# Example usage
user_prompt = "what is gulf of america?"
#user_prompt = "What is the capital city of Nepla?"
response=asyncio.run(run_agent(user_prompt))
print(response.all_messages)
print(response.data)

# user_prompt = "What is the capital city of Nepla?"
# response=asyncio.run(run_agent(user_prompt))


