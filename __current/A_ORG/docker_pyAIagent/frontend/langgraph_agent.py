from langgraph.prebuilt import ToolNode
from langgraph.graph import <PERSON><PERSON>raph, END, MessageGraph, START
from langchain_core.messages import HumanMessage,BaseMessage
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_openai import Chat<PERSON>penAI
from IPython.display import Image, display

# Optional, add tracing in LangSmith
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "Demo-Agent"

tavily_tool = TavilySearchResults(max_results=5)
tools = [tavily_tool]

model = ChatOpenAI(model='gpt-4o')

model_with_tools = model.bind_tools(tools=tools)

builder = MessageGraph()
builder.add_node('model', model_with_tools)
tool_node = ToolNode(tools)
builder.add_node("tool", tool_node)
builder.add_edge(START, "model")

def router(state: list[BaseMessage]):
    tool_calls = state[-1].additional_kwargs.get("tool_calls", [])
    if len(tool_calls):
        return "tool"
    else:
        return END

builder.add_conditional_edges("model", router)
builder.add_edge("tool", 'model')

graph = builder.compile()
display(Image(graph.get_graph(xray=True).draw_mermaid_png()))
